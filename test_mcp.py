#!/usr/bin/env python3
"""
MCP集成测试脚本
"""
import asyncio
import sys
from utils.mcp_client import mcp_client
from utils.llm import get_llm_response


async def test_mcp_connection():
    """测试MCP连接"""
    print("=== 测试MCP连接 ===")
    
    try:
        await mcp_client.initialize()
        if mcp_client.is_initialized:
            print("✅ MCP客户端初始化成功")
            
            # 获取可用工具
            tools = mcp_client.get_available_tools()
            print(f"📋 发现 {len(tools)} 个可用工具:")
            for tool in tools:
                print(f"  - {tool.get('name', 'Unknown')}: {tool.get('description', 'No description')}")
                
        else:
            print("❌ MCP客户端初始化失败")
            
    except Exception as e:
        print(f"❌ MCP连接测试失败: {str(e)}")
        

async def test_mcp_tool_call():
    """测试MCP工具调用"""
    print("\n=== 测试MCP工具调用 ===")
    
    if not mcp_client.is_initialized:
        print("❌ MCP客户端未初始化，跳过工具调用测试")
        return
        
    try:
        # 测试消息
        test_messages = [
            {"role": "user", "content": "请帮我查询一下商品信息"}
        ]
        
        # 检查是否应该使用工具
        should_use = mcp_client.should_use_tools(test_messages)
        print(f"🔍 是否需要使用工具: {should_use}")
        
        if should_use:
            # 处理消息
            processed_messages = await mcp_client.process_with_tools(test_messages)
            print(f"📝 处理后的消息数量: {len(processed_messages)}")
            
            for i, msg in enumerate(processed_messages):
                print(f"  消息 {i+1}: {msg['role']} - {msg['content'][:100]}...")
                
    except Exception as e:
        print(f"❌ 工具调用测试失败: {str(e)}")


async def test_llm_with_mcp():
    """测试LLM与MCP集成"""
    print("\n=== 测试LLM与MCP集成 ===")
    
    try:
        # 测试消息
        test_messages = [
            {"role": "system", "content": "你是一个智能客服助手，可以帮助用户查询商品信息。"},
            {"role": "user", "content": "你好，我想查询一些商品信息"}
        ]
        
        print("🤖 正在调用LLM（启用MCP）...")
        response = await get_llm_response(test_messages, use_mcp=True)
        
        if response.success:
            print("✅ LLM调用成功")
            print(f"📊 Token使用情况: {response.prompt_tokens}/{response.completion_tokens}/{response.all_tokens}")
            print(f"💭 回答: {response.answer[:200]}...")
        else:
            print(f"❌ LLM调用失败: {response.answer}")
            
    except Exception as e:
        print(f"❌ LLM集成测试失败: {str(e)}")


async def main():
    """主测试函数"""
    print("🚀 开始MCP集成测试\n")
    
    # 测试MCP连接
    await test_mcp_connection()
    
    # 测试工具调用
    await test_mcp_tool_call()
    
    # 测试LLM集成
    await test_llm_with_mcp()
    
    # 清理
    await mcp_client.close()
    print("\n🏁 测试完成")


if __name__ == "__main__":
    asyncio.run(main())
