from typing import Optional

from pydantic import BaseModel


class LLMResponse(BaseModel):
    success: bool
    reasoning_content: str
    answer: str
    prompt_tokens: int
    completion_tokens: int
    all_tokens: int


class ChatDataResponse(LLMResponse):
    user_id: str = ""
    chat_id: str = ""
    msg_id: str = ""


class ChatResponse(ChatDataResponse):
    code: int = 200
    message: str = "success"
    data: Optional[LLMResponse] = None

    @classmethod
    def from_llm_response(cls, llm_response: LLMResponse, **kwargs):
        """从 LLMResponse 创建 ChatResponse"""
        return cls(
            success=llm_response.success,
            reasoning_content=llm_response.reasoning_content,
            answer=llm_response.answer,
            prompt_tokens=llm_response.prompt_tokens,
            completion_tokens=llm_response.completion_tokens,
            all_tokens=llm_response.all_tokens,
            data=llm_response.model_dump(),
            **kwargs
        )


# 首先创建一个简单的 LLMResponse
basic_llm_response = LLMResponse(
    success=True,
    reasoning_content="This is a reasoning content.",
    answer="This is the final answer.",
    prompt_tokens=10,
    completion_tokens=20,
    all_tokens=30
)

# 使用辅助方法从 LLMResponse 创建一个 ChatResponse
chat_response = ChatResponse.from_llm_response(
    basic_llm_response,
    user_id="12345",
    chat_id="67890",
    msg_id="abcde",
    code=400,
    message="自定义消息"
)

print(chat_response.model_dump())