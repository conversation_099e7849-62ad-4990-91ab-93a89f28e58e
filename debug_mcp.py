#!/usr/bin/env python3
"""
MCP服务调试脚本
用于测试和调试MCP服务连接
"""
import asyncio
import httpx
import json
from loguru import logger


async def test_mcp_endpoints():
    """测试MCP服务的各种端点"""
    base_url = "http://127.0.0.1:8888/mcp"
    
    # 要测试的端点列表
    endpoints = [
        f"{base_url}",
        f"{base_url}/",
        f"{base_url}/tools",
        f"{base_url}/list_tools",
        f"{base_url}/status",
        f"{base_url}/health",
        f"{base_url}/info"
    ]
    
    print(f"🔍 测试MCP服务端点: {base_url}")
    print("=" * 50)
    
    async with httpx.AsyncClient(timeout=10.0) as client:
        for endpoint in endpoints:
            try:
                print(f"\n📡 测试端点: {endpoint}")
                response = await client.get(endpoint)
                
                print(f"   状态码: {response.status_code}")
                print(f"   响应头: {dict(response.headers)}")
                
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"   JSON响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                    except:
                        text = response.text
                        print(f"   文本响应: {text[:200]}...")
                else:
                    print(f"   错误响应: {response.text[:200]}")
                    
            except Exception as e:
                print(f"   ❌ 连接失败: {str(e)}")


async def test_mcp_tool_call():
    """测试MCP工具调用"""
    base_url = "http://127.0.0.1:8888/mcp"
    
    # 要测试的调用端点
    call_endpoints = [
        f"{base_url}/call_tool",
        f"{base_url}/tools/call",
        f"{base_url}/query",
        f"{base_url}/search"
    ]
    
    # 测试数据
    test_payloads = [
        {
            "method": "tools/call",
            "params": {
                "name": "商品查询",
                "arguments": {"query": "手机"}
            }
        },
        {
            "name": "商品查询",
            "arguments": {"query": "手机"}
        },
        {
            "query": "手机"
        },
        {
            "search": "手机"
        }
    ]
    
    print(f"\n🛠️ 测试MCP工具调用")
    print("=" * 50)
    
    async with httpx.AsyncClient(timeout=30.0) as client:
        for endpoint in call_endpoints:
            for i, payload in enumerate(test_payloads):
                try:
                    print(f"\n🔧 测试调用: {endpoint}")
                    print(f"   载荷 {i+1}: {json.dumps(payload, ensure_ascii=False)}")
                    
                    response = await client.post(endpoint, json=payload)
                    
                    print(f"   状态码: {response.status_code}")
                    
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            print(f"   ✅ 成功响应: {json.dumps(data, indent=2, ensure_ascii=False)}")
                        except:
                            text = response.text
                            print(f"   ✅ 文本响应: {text[:200]}")
                    else:
                        print(f"   ❌ 错误响应: {response.text[:200]}")
                        
                except Exception as e:
                    print(f"   ❌ 调用失败: {str(e)}")


async def test_service_availability():
    """测试服务可用性"""
    print(f"\n🏥 测试服务可用性")
    print("=" * 50)
    
    # 测试基本连接
    try:
        async with httpx.AsyncClient(timeout=5.0) as client:
            response = await client.get("http://127.0.0.1:8888")
            print(f"✅ 基础服务可用: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ 基础服务不可用: {str(e)}")
    
    # 测试MCP端点
    try:
        async with httpx.AsyncClient(timeout=5.0) as client:
            response = await client.get("http://127.0.0.1:8888/mcp")
            print(f"✅ MCP端点可用: HTTP {response.status_code}")
    except Exception as e:
        print(f"❌ MCP端点不可用: {str(e)}")


async def main():
    """主函数"""
    print("🚀 开始MCP服务调试")
    print("请确保你的MCP服务正在 http://127.0.0.1:8888 上运行")
    
    # 测试服务可用性
    await test_service_availability()
    
    # 测试各种端点
    await test_mcp_endpoints()
    
    # 测试工具调用
    await test_mcp_tool_call()
    
    print("\n🏁 调试完成")
    print("\n💡 提示:")
    print("1. 如果所有端点都失败，请检查MCP服务是否正在运行")
    print("2. 如果某些端点成功，请记录成功的端点格式")
    print("3. 根据成功的格式调整MCP客户端配置")


if __name__ == "__main__":
    asyncio.run(main())
