import os
from dotenv import load_dotenv

# 加载 .env 文件中的环境变量（如果存在）
load_dotenv()

# API 配置
OPENAI_API_KEY = os.environ.get("OPENAI_API_KEY")
OPENAI_BASE_URL = os.environ.get("OPENAI_BASE_URL")
OPENAI_MODEL = os.environ.get("OPENAI_MODEL")
MAX_TOKENS = int(os.environ.get("OPENAI_MAX_TOKENS", "40960"))
TEMPERATURE = float(os.environ.get("OPENAI_TEMPERATURE", "0.7"))

# 服务器配置
HOST = os.environ.get("API_HOST", "0.0.0.0")
PORT = int(os.environ.get("API_PORT", 8000))
API_TOKEN = os.environ.get("API_TOKEN", "1234567890")