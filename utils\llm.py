import re
import traceback
from openai import AsyncOpenAI
import config
from pydantic import BaseModel
from typing import List, Dict, Any

def get_think_and_answer(content: str) -> tuple[str, str]:
    think_pattern = r'<think>(.*?)</think>'
    think_match = re.search(think_pattern, content, re.DOTALL)

    if think_match:
        # 提取reasoning_content
        reasoning_content = think_match.group(1).strip()
        # 从内容中删除<think></think>块
        answer = re.sub(think_pattern, '', content, flags=re.DOTALL).strip()
    else:
        reasoning_content = ''
        answer = content.strip()

    return reasoning_content, answer


class LLMResponse(BaseModel):
    success: bool
    reasoning_content: str
    answer: str
    prompt_tokens: int
    completion_tokens: int
    all_tokens: int



def get_client() -> AsyncOpenAI:
    """
    获取异步OpenAI客户端实例。
    该函数从配置文件中读取API密钥和基础URL，并返回一个AsyncOpenAI客户端实例。
    """
    client = AsyncOpenAI(
        api_key=config.OPENAI_API_KEY,  # 从配置文件读取API密钥
        base_url=config.OPENAI_BASE_URL  # 从配置文件读取基础URL
    )
    return client


async def get_llm_response(messages: list, use_mcp: bool = True) -> LLMResponse:
    """
    使用指定的模型和消息列表获取LLM响应。
    """
    try:
        # 如果启用MCP，先处理工具调用
        processed_messages = messages
        if use_mcp:
            try:
                from .mcp_client import mcp_client
                processed_messages = await mcp_client.process_with_tools(messages)
            except Exception as mcp_error:
                # MCP处理失败时记录错误但继续正常流程
                import traceback
                traceback.print_exc()
                print(f"MCP处理失败，继续正常流程: {str(mcp_error)}")

        # 获取异步OpenAI客户端
        client = get_client()
        # 异步调用获取响应
        response = await client.chat.completions.create(
            model=config.OPENAI_MODEL,
            messages=processed_messages,
            temperature=config.TEMPERATURE,
            max_tokens=config.MAX_TOKENS,
            stream=False
        )
        if response and response.choices:
            content = response.choices[0].message.content.strip()
            reasoning_content, answer = get_think_and_answer(content)
            prompt_tokens = response.usage.prompt_tokens
            completion_tokens = response.usage.completion_tokens
            all_tokens = response.usage.total_tokens

            result = LLMResponse(
                success=True,
                reasoning_content=reasoning_content,
                answer=answer,
                prompt_tokens=prompt_tokens,
                completion_tokens=completion_tokens,
                all_tokens=all_tokens
            )
        else:
            result = LLMResponse(
                success=False,
                reasoning_content="",
                answer="No response from the model.",
                prompt_tokens=0,
                completion_tokens=0,
                all_tokens=0
            )

    except Exception as e:
        traceback.print_exc()
        result = LLMResponse(
            success=False,
            reasoning_content="",
            answer=f"Error: {str(e)}",
            prompt_tokens=0,
            completion_tokens=0,
            all_tokens=0
        )

    return result