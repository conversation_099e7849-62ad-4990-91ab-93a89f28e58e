import sys
from loguru import logger
from pathlib import Path

def set_up_log():
    # 创建logs文件夹（如果不存在）
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)

    # 配置loguru日志
    # 移除默认的处理程序
    logger.remove()
    # 添加控制台输出
    logger.add(sys.stderr, level="INFO")
    # 添加文件输出，按日期轮转
    logger.add(
        "logs/app_{time:YYYY-MM-DD}.log",
        rotation="00:00",  # 每天午夜轮转
        retention="30 days",  # 保留30天的日志
        compression="zip",  # 压缩旧日志
        level="DEBUG",  # 文件中记录所有级别的日志
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",  # 设置日志格式
        enqueue=True  # 异步写入
    )