#!/usr/bin/env python3
"""
演示如何在聊天中使用MCP服务
"""
import asyncio
from utils.mcp_client import mcp_client
from utils.llm import get_llm_response


async def demo_chat():
    """演示聊天功能"""
    print("🤖 AI聊天演示（集成MCP商品查询）")
    print("=" * 50)
    
    # 初始化MCP客户端
    await mcp_client.initialize()
    
    # 测试对话
    test_conversations = [
        {
            "name": "普通对话",
            "messages": [
                {"role": "system", "content": "你是一个智能客服助手。"},
                {"role": "user", "content": "你好，请介绍一下你自己"}
            ]
        },
        {
            "name": "商品查询对话",
            "messages": [
                {"role": "system", "content": "你是一个智能客服助手，可以帮助用户查询商品信息。"},
                {"role": "user", "content": "我想查询一下手机的价格和库存信息"}
            ]
        },
        {
            "name": "具体商品查询",
            "messages": [
                {"role": "system", "content": "你是一个智能客服助手，可以帮助用户查询商品信息。"},
                {"role": "user", "content": "请帮我查询iPhone 15 Pro的详细信息"}
            ]
        }
    ]
    
    for i, conversation in enumerate(test_conversations, 1):
        print(f"\n📝 测试对话 {i}: {conversation['name']}")
        print("-" * 30)
        
        # 显示对话历史
        for msg in conversation["messages"]:
            role_emoji = "🤖" if msg["role"] == "system" else "👤"
            print(f"{role_emoji} {msg['role'].upper()}: {msg['content']}")
        
        print("\n🔄 处理中...")
        
        try:
            # 调用LLM（启用MCP）
            response = await get_llm_response(conversation["messages"], use_mcp=True)
            
            if response.success:
                print(f"✅ 回答: {response.answer}")
                print(f"📊 Token使用: {response.prompt_tokens}/{response.completion_tokens}/{response.all_tokens}")
                
                if response.reasoning_content:
                    print(f"💭 思考过程: {response.reasoning_content}")
            else:
                print(f"❌ 错误: {response.answer}")
                
        except Exception as e:
            print(f"❌ 处理失败: {str(e)}")
        
        print("\n" + "="*50)
    
    # 清理
    await mcp_client.close()


async def interactive_chat():
    """交互式聊天"""
    print("\n🎯 交互式聊天模式")
    print("输入 'quit' 退出，输入 'help' 查看帮助")
    print("-" * 50)
    
    # 初始化MCP客户端
    await mcp_client.initialize()
    
    # 对话历史
    messages = [
        {"role": "system", "content": "你是一个智能客服助手，可以帮助用户查询商品信息。当用户询问商品相关问题时，你会尝试调用商品查询工具来获取准确信息。"}
    ]
    
    while True:
        try:
            # 获取用户输入
            user_input = input("\n👤 您: ").strip()
            
            if user_input.lower() == 'quit':
                print("👋 再见！")
                break
            elif user_input.lower() == 'help':
                print("💡 帮助:")
                print("- 您可以询问任何问题")
                print("- 特别是商品相关的查询（价格、库存、功能等）")
                print("- 输入 'quit' 退出")
                print("- 输入 'clear' 清空对话历史")
                continue
            elif user_input.lower() == 'clear':
                messages = [messages[0]]  # 保留系统消息
                print("🧹 对话历史已清空")
                continue
            elif not user_input:
                continue
            
            # 添加用户消息
            messages.append({"role": "user", "content": user_input})
            
            print("🔄 思考中...")
            
            # 调用LLM
            response = await get_llm_response(messages, use_mcp=True)
            
            if response.success:
                print(f"🤖 助手: {response.answer}")
                
                # 添加助手回复到历史
                messages.append({"role": "assistant", "content": response.answer})
                
                # 显示token使用情况
                print(f"📊 Token: {response.prompt_tokens}+{response.completion_tokens}={response.all_tokens}")
                
            else:
                print(f"❌ 抱歉，出现了错误: {response.answer}")
                
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {str(e)}")
    
    # 清理
    await mcp_client.close()


async def main():
    """主函数"""
    print("🚀 MCP集成聊天演示")
    
    # 运行演示
    await demo_chat()
    
    # 询问是否进入交互模式
    try:
        choice = input("\n🎮 是否进入交互式聊天模式？(y/n): ").strip().lower()
        if choice in ['y', 'yes', '是']:
            await interactive_chat()
    except KeyboardInterrupt:
        print("\n👋 再见！")


if __name__ == "__main__":
    asyncio.run(main())
