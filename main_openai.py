import os
import time
import uuid
import uvicorn
import traceback
from loguru import logger
from pydantic import BaseModel
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, HTTPException, Depends, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.security import HTTPAuthorizationCredentials
from fastapi.exceptions import RequestValidationError
from fastapi.responses import JSONResponse
from typing import Optional, List, Dict, Any, Union
import config
from utils.llm import get_llm_response, LLMResponse
from utils.validate import verify_token, CustomHTTPException
from utils.middleware import LogRequestMiddleware
from utils.log import set_up_log

set_up_log()


# 应用的生命周期管理器
@asynccontextmanager
async def lifespan(app: FastAPI):
    logger.info("=== 智能客服系统服务启动 ===")
    logger.info(f"使用模型: {config.OPENAI_MODEL}")
    logger.info(f"API基础URL: {config.OPENAI_BASE_URL}")
    yield
    logger.info("=== 智能客服系统服务关闭 ===")


app = FastAPI(
    title="智能客服系统",
    description="基于大模型的智能问答API",
    lifespan=lifespan
)

# 配置 CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# app.add_middleware(LogRequestMiddleware)


class ErrorResponse(BaseModel):
    code: int = 400  # 状态码字段
    message: str  # 错误详情
    data: Optional[Dict] = {}  # 错误数据

# 定义消息模型，完全兼容OpenAI的格式
class ChatMessage(BaseModel):
    role: str
    content: Optional[Union[str, List[Dict[str, Any]]]] = None
    name: Optional[str] = None
    function_call: Optional[Dict[str, Any]] = None


class ChatRequest(BaseModel):
    user_id: str = ""
    chat_id: str = ""
    msg_id: str = ""
    prompt: str = ""
    messages: Optional[List[ChatMessage]]


class ChatDataResponse(LLMResponse):
    user_id: str = ""
    chat_id: str = ""
    msg_id: str = ""
    answer_id: str = ""


class ChatResponse(BaseModel):
    code: int = 200
    message: str = "success"
    data: Optional[ChatDataResponse] = None



# 添加自定义异常处理器
@app.exception_handler(CustomHTTPException)
async def custom_exception_handler(request: Request, exc: CustomHTTPException):
    return JSONResponse(
        status_code=exc.code,
        content={
            "code": exc.code,
            "message": exc.message,
            "data": exc.data
        }
    )


@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    """自定义请求体参数校验失败返回内容"""
    errors = exc.errors()
    first_error = errors[0] if errors else {}
    data = {
        "error": "参数错误",
        "type": first_error.get("type"),
        "loc": first_error.get("loc"),
        "msg": first_error.get("msg"),
    }
    response = ErrorResponse(
        code=status.HTTP_400_BAD_REQUEST,
        message="参数错误",
        data=data
    )
    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content=response
    )


# 生成完整回答
async def generate_full_response(messages: List[ChatMessage], answer_id) -> LLMResponse:
    try:
        # 调用OpenAI API生成完整回答
        logger.debug(f"[{answer_id}] 开始调用API生成完整回答")
        start_time = time.time()
        llm_response = await get_llm_response(messages)
        api_time = time.time() - start_time
        logger.info(f"[{answer_id}] API调用成功, 耗时: {api_time:.4f}秒")
        return llm_response
    except Exception as e:
        traceback.print_exc()
        logger.error(f"[{answer_id}] 完整响应生成错误：{str(e)}", exc_info=True)
        return LLMResponse(
            success=False,
            reasoning_content="",
            answer=f"Error: {str(e)}",
            prompt_tokens=0,
            completion_tokens=0,
            all_tokens=0
        )


# 聊天API端点 - POST方法（完整请求），兼容OpenAI格式
@app.post("/api/chat")
async def chat_post(
        request: ChatRequest,
        credentials: HTTPAuthorizationCredentials = Depends(verify_token)
):
    # 如果没有messages或者为空，则抛出错误
    if not request.messages:
        raise HTTPException(
            status_code=400,
            detail="缺少有效的查询内容，请提供messages"
        )

    # 创建唯一ID和时间戳
    answer_id = f"{uuid.uuid4().hex}".replace("-", "")
    logger.info(f"[新请求][{answer_id}] 用户ID: {request.user_id}, 聊天ID: {request.chat_id}, 消息ID: {request.msg_id}, 问题：{request.messages[-1].content}")
    response = await generate_full_response(messages=request.messages, answer_id=answer_id)
    response_data = ChatDataResponse(
        user_id=request.user_id,
        chat_id=request.chat_id,
        msg_id=request.msg_id,
        answer_id=answer_id,
        success=response.success,
        reasoning_content=response.reasoning_content,
        answer=response.answer,
        prompt_tokens=response.prompt_tokens,
        completion_tokens=response.completion_tokens,
        all_tokens=response.all_tokens
    )
    if response.success:
        logger.info(f"[{answer_id}] {response_data.model_dump()}")
        return ChatResponse(
            code=200,
            message="success",
            data=response_data
        )
    else:
        logger.error(f"[{answer_id}] 响应生成失败，错误信息: {response.answer}")
        return ChatResponse(
            code=404,
            message="failed",
            data=response_data
        )


# 挂载静态文件目录
app.mount("/static", StaticFiles(directory="static"), name="static")


# 根路由重定向到演示页面
@app.get("/")
async def root():
    from fastapi.responses import RedirectResponse
    return RedirectResponse(url="/static/index.html")


# 启动服务
if __name__ == "__main__":
    logger.info("正在启动服务器...")
    uvicorn.run(app, host="0.0.0.0", port=8000)
