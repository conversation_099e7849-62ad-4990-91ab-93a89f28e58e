# 智能客服系统

智能客服是一个基于自然语言处理和机器学习的机器人，使用大模型作为后端，与客户进行智能问答。本项目提供REST API，支持流式输出（SSE）和普通响应两种方式。

## 功能特性

- 基于OpenAI API兼容接口的大模型集成
- 完全兼容OpenAI API格式，支持直接使用OpenAI客户端
- 支持通过base_url配置自定义模型服务器
- 支持对话历史记录
- 提供标准REST API接口
- 支持SSE流式输出以提升实时性体验
- tiktoken精确token计数
- 支持模型思考过程隔离（使用<think></think>标签）
- loguru高级日志功能，支持按日期轮转和压缩
- 简洁高效的架构设计

## 安装与配置

1. 克隆项目代码

```bash
git clone <your-repository-url>
cd ai-chat
```

2. 安装依赖项

```bash
pip install -r requirements.txt
```

3. 配置环境变量

```bash
# 创建或编辑.env文件
nano .env  # 或使用任何文本编辑器
```

在`.env`文件中需要配置以下内容:
```
OPENAI_API_KEY=your_api_key_here
OPENAI_BASE_URL=http://your-api-server-url/v1  # OpenAI API兼容的服务器URL
OPENAI_MODEL=model_name  # 例如 qwen32b, gpt-3.5-turbo等
```

可选配置项:
- 系统提示词
- 服务器配置
- 生成参数设置

## 启动服务

执行以下命令启动API服务器：

```bash
# 方式1: 使用启动脚本（默认使用main.py）
./start.sh

# 方式2: 指定使用main_new.py（tiktoken和loguru增强版本）
./start.sh -v new

# 方式3: 指定使用main_openai.py（完全兼容OpenAI API的版本）
./start.sh -v openai
```

服务将在 `http://localhost:8000` 上运行，可以通过浏览器访问以下地址：
- `http://localhost:8000`：演示页面
- `http://localhost:8000/docs`：API文档（Swagger UI）

如果使用main_openai.py版本，您可以像访问OpenAI API一样访问 `http://localhost:8000/v1/chat/completions`。

## API使用说明

### 获取可用模型

**请求**

```http
GET /api/models
```

**响应**

```json
{
  "models": [
    {
      "id": "qwen32b",
      "name": "qwen32b",
      "provider": "openai",
      "maxTokens": 4096
    }
  ]
}
```

### 聊天接口

#### 传统格式请求

**请求**

```http
POST /api/chat
Content-Type: application/json

{
  "query": "你好，请介绍一下你自己",
  "stream": false,
  "history": [
    {
      "user": "你叫什么名字？",
      "assistant": "我是智能客服助手，很高兴为您服务。"
    }
  ],
  "model": "qwen32b"
}
```

**参数说明**

- `query`: 用户的问题或指令 (必填)
- `stream`: 是否使用流式输出 (可选，默认为false)
- `history`: 对话历史记录 (可选)
- `model`: 使用的模型名称 (可选，默认使用OPENAI_MODEL环境变量指定的模型)

#### OpenAI格式请求 (推荐)

**请求**

```http
POST /api/chat
Content-Type: application/json

{
  "model": "qwen32b",
  "messages": [
    {"role": "system", "content": "你是一个智能客服助手，你的任务是礼貌地回答用户的问题。"},
    {"role": "user", "content": "你叫什么名字？"},
    {"role": "assistant", "content": "我是智能客服助手，很高兴为您服务。"},
    {"role": "user", "content": "你好，请介绍一下你自己"}
  ],
  "temperature": 0.7,
  "stream": false,
  "max_tokens": 2000
}
```

**参数说明**

- `model`: 使用的模型名称 (可选，默认使用OPENAI_MODEL环境变量指定的模型)
- `messages`: 消息列表，每条消息包含role和content (必填)
- `temperature`: 采样温度，控制随机性，值越低越确定性 (可选，默认为0.7)
- `stream`: 是否使用流式输出 (可选，默认为false)
- `max_tokens`: 生成的最大token数 (可选，默认为2000)
- 其它OpenAI API兼容参数: `top_p`, `frequency_penalty`, `presence_penalty`等

**普通响应**

当 `stream=false` 时，将返回完整的JSON响应（OpenAI格式）：

```json
{
  "id": "chatcmpl-123abc456def",
  "object": "chat.completion",
  "created": **********,
  "model": "qwen32b",
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "您好！我是一个智能客服助手，基于人工智能技术开发。我可以回答您的问题，提供信息和帮助解决问题。请问有什么我可以帮助您的吗？",
        "reasoning_content": "用户在问我自己的情况，我应该介绍一下自己的功能和定位。"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 56,
    "completion_tokens": 37,
    "total_tokens": 93,
    "completion_tokens_details": {
      "reasoning_tokens": 25
    }
  }
}
```

**流式响应**

当 `stream=true` 时，将返回一个SSE流，每个事件为模型生成的文本片段，格式符合OpenAI的流式响应格式：

```
data: {"id":"chatcmpl-123abc456def","object":"chat.completion.chunk","created":**********,"model":"qwen32b","choices":[{"index":0,"delta":{"content":"您好","role":"assistant"},"finish_reason":null}],"system_fingerprint":"","usage":{"prompt_tokens":56,"completion_tokens":1,"total_tokens":57}}
data: {"id":"chatcmpl-123abc456def","object":"chat.completion.chunk","created":**********,"model":"qwen32b","choices":[{"index":0,"delta":{"content":"！我","role":"assistant"},"finish_reason":null}],"system_fingerprint":"","usage":{"prompt_tokens":56,"completion_tokens":2,"total_tokens":58}}
data: {"id":"chatcmpl-123abc456def","object":"chat.completion.chunk","created":**********,"model":"qwen32b","choices":[{"index":0,"delta":{"content":"是一个","role":"assistant"},"finish_reason":null}],"system_fingerprint":"","usage":{"prompt_tokens":56,"completion_tokens":5,"total_tokens":61}}
...
```

如果模型生成了推理内容（使用`<think></think>`标签），API会在delta中返回`reasoning_content`字段。

## 健康检查

```http
GET /health

Response:
{
  "status": "healthy"
}
```

## 完全兼容OpenAI API

本系统提供了完全兼容OpenAI API的端点，可以直接替代OpenAI API使用。支持以下端点：

### 聊天补全API

```http
POST /v1/chat/completions
Content-Type: application/json

{
  "model": "qwen32b",
  "messages": [
    {"role": "system", "content": "你是一个智能客服助手。"},
    {"role": "user", "content": "你好，请介绍一下你自己"}
  ],
  "temperature": 0.7,
  "stream": false
}
```

### 模型列表API

```http
GET /v1/models

Response:
{
  "object": "list",
  "data": [
    {
      "id": "qwen32b",
      "object": "model",
      "created": **********,
      "owned_by": "organization-owner"
    }
  ]
}
```

## OpenAI客户端使用示例

本项目提供了一个示例脚本`openai_client_example.py`，演示如何使用OpenAI官方库连接我们的API：

```python
from openai import OpenAI

# 创建OpenAI客户端
client = OpenAI(
    api_key="dummy-key",  # 这个API密钥会被忽略，但需要提供一个非空值
    base_url="http://localhost:8000/v1"  # 指向我们的API
)

# 调用API
response = client.chat.completions.create(
    model="qwen32b",
    messages=[
        {"role": "system", "content": "你是一个智能客服助手。"},
        {"role": "user", "content": "你好，请介绍一下你自己"}
    ]
)

print(response.choices[0].message.content)
```

运行示例：

```bash
# 普通请求
python openai_client_example.py --query "你好，请介绍一下你自己"

# 流式请求
python openai_client_example.py --query "你好，请介绍一下你自己" --stream
```
